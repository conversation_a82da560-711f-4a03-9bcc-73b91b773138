import React, { useState, useCallback } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Radio, Checkbox, Row, Col, Collapse, Avatar } from 'antd';
import { ModeConfig, TOOL_GROUPS, ToolGroup, GroupEntry } from '@joycoder/agent-driven/web-agent/src/utils/modes';
import { CommonMessage } from '../../messages/messageTypes';
import { ModesInfo } from '../../typings/modes';
import { generateRandomId } from '../../utils/utils';
import './index.scss';
import TabButton from '../common/TabButton';
import AgentList from './AgentList';
import AgentMarket from './AgentMarket';
import defaultAvatar from '../../assets/images/logo_bg_blue.svg';

const { TextArea } = Input;
const { Group: RadioGroup, Button: RadioButton } = Radio;
const { Panel } = Collapse;

interface AgentManagementProps {
  autoApprovalSettings: any;
  modesInfo: ModesInfo;
  setAutoApprovalSettings: (settings: any) => void;
  isRemoteEnvironment: boolean;
}

export default function AgentManagement(props: AgentManagementProps): JSX.Element {
  const { autoApprovalSettings, modesInfo, setAutoApprovalSettings, isRemoteEnvironment } = props;

  const [isShowAgentModel, setIsShowAgentModel] = useState(false);
  const [agentTile, setAgentTile] = useState('新建智能体');
  const [newModeName, setNewModeName] = useState('');
  const [newModeSlug, setNewModeSlug] = useState('');
  const [newModeRoleDefinition, setNewModeRoleDefinition] = useState('');
  const [newModeWhenToUse, setNewModeWhenToUse] = useState('');
  const [newModeCustomInstructions, setNewModeCustomInstructions] = useState('');
  const [newModeGroups, setNewModeGroups] = useState<GroupEntry[]>([]);
  const [newModeSource, setNewModeSource] = useState<'global' | 'project'>('global');
  const [agentHeadImg, setAgentHeadImg] = useState(null);
  const [currentAgentInfo, setCurrentAgentInfo] = useState<ModeConfig>({} as ModeConfig);
  const [activeTab, setActiveTab] = useState('list');

  const [nameError, setNameError] = useState<string>('');
  const [slugError, setSlugError] = useState<string>('');
  const [roleDefinitionError, setRoleDefinitionError] = useState<string>('');
  const [groupsError, setGroupsError] = useState<string>('');

  const availableGroups = (Object.keys(TOOL_GROUPS) as ToolGroup[]).filter(
    (group) => !TOOL_GROUPS[group].alwaysAvailable
  );

  const groupNames: Record<ToolGroup, string> = {
    read: '读取文件',
    edit: '编辑文件',
    browser: '浏览器',
    command: '运行命令',
    mcp: 'MCP服务',
    modes: '模式',
  };

  const handleNameChange = (name: string) => {
    setNewModeName(name);
    if (!newModeSlug) {
      setNewModeSlug(generateRandomId());
    }
  };

  const updateCustomMode = useCallback((agentId: string, modeConfig: ModeConfig) => {
    const source = modeConfig.source || 'global';
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'updateCustomMode',
          agentId,
          modeConfig: {
            ...modeConfig,
            source,
          },
        },
      },
    });
  }, []);

  const switchMode = useCallback((agentId: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'joycoder-set-mode',
        data: {
          text: agentId,
        },
      },
    });
  }, []);

  const onCreateMode = (newMode: ModeConfig): void => {
    updateCustomMode(newMode.agentId, newMode);
    switchMode(newMode.agentId);
    setIsShowAgentModel(false);
  };

  const handleCreateAgent = () => {
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');

    let isValid = true;

    if (!newModeName?.trim()) {
      setNameError('请输入智能体名称');
      isValid = false;
    }

    // if (!newModeSlug?.trim()) {
    //   setSlugError('请输入有效的 agentId');
    //   isValid = false;
    // }

    if (!newModeRoleDefinition?.trim()) {
      setRoleDefinitionError('请输入角色定义');
      isValid = false;
    }

    if (newModeGroups.length === 0) {
      setGroupsError('请至少选择一个工具');
      isValid = false;
    }

    if (isValid) {
      onCreateMode({
        name: newModeName?.trim(),
        agentId: newModeSlug?.trim(),
        agentDefinition: newModeRoleDefinition?.trim(),
        whenToUse: newModeWhenToUse?.trim() || undefined,
        customInstructions: newModeCustomInstructions?.trim() || undefined,
        groups: newModeGroups,
        source: newModeSource,
        isActive: true,
      });
      setIsShowAgentModel(false);
    }
  };

  const resetModalInfo = () => {
    setNewModeName('');
    setNewModeSlug('');
    setNewModeRoleDefinition('');
    setNewModeWhenToUse('');
    setNewModeCustomInstructions('');
    setNewModeGroups([]);
    setNewModeSource('global');
    setNameError('');
    setSlugError('');
    setRoleDefinitionError('');
    setGroupsError('');
    setCurrentAgentInfo({} as ModeConfig);
  };

  const handleEditorAgent = (agentId: string) => {
    const currentAgentInfo =
      modesInfo?.customModes.find((m: ModeConfig) => m.agentId === agentId) || ({} as ModeConfig);
    setCurrentAgentInfo(currentAgentInfo);

    setAgentTile('编辑智能体');
    setNewModeName(currentAgentInfo.name);
    setNewModeSlug(currentAgentInfo.agentId);
    setNewModeRoleDefinition(currentAgentInfo.agentDefinition);
    setNewModeWhenToUse(currentAgentInfo.whenToUse ?? '');
    setNewModeCustomInstructions(currentAgentInfo.customInstructions ?? '');
    setNewModeGroups(currentAgentInfo.groups);
    setNewModeSource(currentAgentInfo.source);

    setIsShowAgentModel(true);
  };

  const handleDelAgent = (agentId: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'deleteCustomMode',
          agentId,
        },
      },
    });
  };

  const handleToggleAgent = (agentId: string, isActive: boolean) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'toggleCustomMode',
          agentId,
          isActive,
        },
      },
    });
  };

  const handleOpenFile = (filePath: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-mode-operation',
        data: {
          action: 'openFile',
          filePath: `.joycode/${filePath}`,
          agentTile: agentTile,
        },
      },
    });
  };

  return (
    <div className="setting-tabs-item">
      <div className="setting-tabs-item-title">智能体</div>
      <div className="joycoder-setting-box">
        <div className="agent-tab-buttons">
          <div style={{ marginRight: '15px' }}>
            <TabButton active={activeTab === 'list'} onClick={() => setActiveTab('list')}>
              智能体列表
            </TabButton>
          </div>
          {/* <TabButton active={activeTab === 'market'} onClick={() => setActiveTab('market')}>
            智能体市场
          </TabButton> */}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setAgentTile('新建智能体');
              resetModalInfo();
              setIsShowAgentModel(true);
            }}
            style={{
              backgroundColor: 'var(--vscode-button-secondaryBackground, #72747c)',
              borderColor: 'var(--vscode-input-border, #72747c)',
              width: '106px',
              height: '28px',
              borderRadius: '4px',
              color: 'var(--vscode-button-secondaryForeground, #72747c)',
              fontSize: '12px',
              padding: '0',
              float: 'right',
              marginLeft: 'auto',
            }}
          >
            新建智能体
          </Button>
        </div>
        {activeTab === 'list' && (
          <AgentList
            modesInfo={modesInfo}
            autoApprovalSettings={autoApprovalSettings}
            setAutoApprovalSettings={setAutoApprovalSettings}
            onEditAgent={handleEditorAgent}
            onDeleteAgent={handleDelAgent}
            onToggleAgent={handleToggleAgent}
          />
        )}
        {activeTab === 'market' && <AgentMarket />}
      </div>

      {/* 新建智能体 */}
      <Modal
        title={agentTile}
        open={isShowAgentModel}
        onCancel={() => {
          resetModalInfo();
          setIsShowAgentModel(false);
        }}
        onOk={handleCreateAgent}
        destroyOnClose={true}
        okText="保存"
        cancelText="取消"
        style={{ backgroundColor: '#18181BFF' }}
        centered={true}
        width={640}
        // 确保Modal在窄屏幕下不会超出视口
        getContainer={() => document.body}
        className="agent-management-modal"
      >
        <div className="agent-info-top">
          <Avatar className="agent-list-avatar fl mr-8" src={agentHeadImg || defaultAvatar} />
          <div className="agent-info-top-title fl">
            <div className="agent-info-top-title-text">
              名称 <span style={{ color: 'red' }}>*</span>
            </div>
            <Input
              placeholder="输入智能体名称"
              defaultValue={currentAgentInfo.name}
              onChange={(e) => handleNameChange(e.target.value)}
              className="agent-info-top-title-input"
              showCount
              maxLength={20}
            />
            <div className="agent-info-top-subtitle color-red">{nameError}</div>
            {/* <div className="agent-info-top-subtitle color-red">{slugError}</div> */}
          </div>
          {/* <div className="agent-info-top-config">
            <a href="https://inner-joycoder.jd.com/" target="_blank">
              <i className="icon iconfont icon-wenhao mr-4"></i>如何配置
            </a>
          </div> */}
        </div>

        <div className="joycoder-setting-box-title mt-24">角色定义</div>
        <div className="agent-info-top-subtitle">
          设定专业方向
          {agentTile === '编辑智能体' && currentAgentInfo.source === 'project' && (
            <>
              ，使用MD：
              <code
                className="agent-info-top-subtitle-code"
                onClick={() =>
                  handleOpenFile(`modes/rules-${currentAgentInfo.agentId || newModeSlug}/agentDefinition.md`)
                }
              >
                编辑
              </code>
            </>
          )}
        </div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.agentDefinition}
          onChange={(e) => {
            setNewModeRoleDefinition(e.target.value);
          }}
          placeholder="请输入角色定义"
          autoSize={{ minRows: 3, maxRows: 6 }}
        />
        <div className="agent-info-top-subtitle color-red">{roleDefinitionError}</div>

        {/* <div className="joycoder-setting-box-title mt-24">使用场景（可选）</div>
        <div className="agent-info-top-subtitle">清晰描述此模式最适合的场景和任务类型</div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.whenToUse}
          onChange={(e) => {
            setNewModeWhenToUse(e.target.value);
          }}
          placeholder="请输入使用场景"
          autoSize={{ minRows: 3, maxRows: 6 }}
        /> */}

        <div className="joycoder-setting-box-title mt-24">自定义指令（可选）</div>
        <div className="agent-info-top-subtitle">
          设置专属规则
          {agentTile === '编辑智能体' && currentAgentInfo.source === 'project' && (
            <>
              ，使用MD：
              <code
                className="agent-info-top-subtitle-code"
                onClick={() =>
                  handleOpenFile(`modes/rules-${currentAgentInfo.agentId || newModeSlug}/customInstructions.md`)
                }
              >
                编辑
              </code>
            </>
          )}
        </div>
        <TextArea
          className="joycoder-setting-textarea mt-4"
          defaultValue={currentAgentInfo.customInstructions}
          onChange={(e) => setNewModeCustomInstructions(e.target.value)}
          placeholder="请输入自定义指令"
          autoSize={{ minRows: 3, maxRows: 6 }}
        />

        <div className="joycoder-setting-box-title mt-26">工具</div>
        <div className="agent-info-top-subtitle">为你的智能体配置工具，智能体将根据任务自动使用它们</div>
        <div className="agent-info-top-box mt-8">
          <div className="joycoder-setting-box-title">内置</div>
          <Checkbox.Group
            defaultValue={currentAgentInfo.groups as string[]} //FIXME: ModeConfig.groups不止是一个string[]，而是支持扩展复杂的数据结构，但目前来说，自定义智能体的groups就是一个string[]，暂时这样处理，之后需要修复
            onChange={(checkedValues) => setNewModeGroups(checkedValues as GroupEntry[])}
            className="w-full mt-8"
          >
            <Row gutter={[16, 8]}>
              {availableGroups.map((group) => (
                <Col span={8} key={group}>
                  <Checkbox value={group}>{groupNames[group]}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>

        <div className="agent-info-top-subtitle color-red">{groupsError}</div>

        <Collapse className="mt-24" ghost>
          <Panel header="高级设置" key="1">
            <div className="joycoder-setting-box-title">作用范围</div>
            <RadioGroup
              defaultValue={newModeSource || (isRemoteEnvironment ? 'project' : 'global')}
              onChange={(e) => setNewModeSource(e.target.value)}
              disabled={agentTile === '编辑智能体'}
            >
              {!isRemoteEnvironment && <RadioButton value="global">全局可用</RadioButton>}
              <RadioButton value="project">当前项目</RadioButton>
            </RadioGroup>
          </Panel>
        </Collapse>
      </Modal>
    </div>
  );
}
