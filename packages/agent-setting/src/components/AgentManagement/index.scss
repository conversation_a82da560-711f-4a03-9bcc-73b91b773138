.joycoder-setting-content .setting-tabs .setting-agent-tabs {
  .joycoder-dark-tabs {
    &-nav {
      padding-top: 0;

    }
    &-nav::before{
      border-bottom: 0;
    }
    &-tab{
      font-size: 15px !important;
    }
  }
}
.joycoder-setting-content {
  .joycoder-setting-box-text {
    .joycoder-dark-input-number {
      border:1px solid var(--vscode-input-foreground, #72747c);
    }
    .joycoder-dark-input-number:hover {
      border:1px solid var(--vscode-input-foreground, #72747c);
    }
    .joycoder-dark-input-number .joycoder-dark-input-number-handler-wrap {
      opacity: 1;
    }
  }
  .joycoder-dark-empty-description {
    color: var(--vscode-descriptionForeground, #72747c);
  }
}

// 修复Modal在窄屏幕下的transform-origin问题
.joycoder-dark-modal-root {
  .joycoder-dark-modal-wrap {
    .joycoder-dark-modal {
      // 确保Modal在窄屏幕下正确居中
      transform-origin: center center !important;

      // 在窄屏幕下调整Modal的定位
      @media (max-width: 768px) {
        max-width: calc(100vw - 32px) !important;
        margin: 16px auto !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        transform-origin: center center !important;
      }

      // 确保Modal内容在窄屏幕下不会溢出
      .joycoder-dark-modal-content {
        @media (max-width: 768px) {
          max-height: calc(100vh - 64px);
          overflow-y: auto;
        }
      }
    }
  }
}

// 专门针对智能体管理Modal的样式修复
.agent-management-modal {
  &.joycoder-dark-modal {
    // 强制设置正确的transform-origin
    transform-origin: center center !important;

    // 在窄屏幕下的特殊处理
    @media (max-width: 768px) {
      max-width: calc(100vw - 32px) !important;
      margin: 16px !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      transform-origin: center center !important;
      position: fixed !important;
    }

    // 确保Modal内容区域正确显示
    .joycoder-dark-modal-content {
      @media (max-width: 768px) {
        max-height: calc(100vh - 64px);
        overflow-y: auto;
        border-radius: 8px;
      }
    }
  }
}

// 全局修复所有Modal的transform-origin问题
.joycoder-dark-modal-wrap {
  .joycoder-dark-modal {
    // 防止transform-origin出现负值
    transform-origin: center center !important;

    @media (max-width: 768px) {
      // 在窄屏幕下强制居中
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      transform-origin: center center !important;
      max-width: calc(100vw - 32px) !important;
      margin: 0 !important;
    }
  }
}